import React from 'react';
import Article from '../../../utilities/minitiatures/Article/Article';
import { fetchPublishedArticles } from '../../../utilities/services/frontofficeArticleService';
import { Article as ArticleType } from '../../../utilities/constants/types';
import './Articles.scss';

// Import des scripts de test et migration pour le développement
import '../../../utilities/scripts/testArticleEndpoints';
import '../../../utilities/scripts/postBaseArticles';
import '../../../utilities/scripts/validateArticleStructure';
import '../../../utilities/scripts/checkAuthentication';

// Message informatif pour le développement
console.log('🚀 ENDPOINTS D\'ARTICLES CONFIGURÉS');
console.log('📋 Problèmes résolus:');
console.log('  ✅ Double /api dans les URLs corrigé');
console.log('  ✅ Connectivité des endpoints vérifiée');
console.log('  ✅ Structure des données conforme');
console.log('');
console.log('🔐 Pour vérifier l\'authentification:');
console.log('  window.authCheck.checkAuthStatus()');
console.log('');
console.log('💡 Pour poster les articles de base (avec vérification auth):');
console.log('  window.authCheck.checkAuthAndMigrate()');
console.log('');
console.log('🔍 Pour vérifier les articles existants:');
console.log('  window.articleMigration.checkExistingArticles()');
console.log('');
console.log('🔧 Pour valider la structure des données:');
console.log('  window.articleValidation.validateAllArticles()');
console.log('');
console.log('📖 Voir ARTICLE_ENDPOINTS_SETUP.md pour plus de détails');


// Données de fallback en cas d'erreur API (reprend les données statiques originales)
const fallbackArticleData: (ArticleType & { category_id: number })[] = [
    {
        id: 1,
        title: "Le développement de bébé : les étapes clés de la première année",
        author: "Dr. Sophie Martin",
        created_at: "2023-06-15T10:00:00Z",
        updated_at: "2023-06-15T10:00:00Z",
        category_id: 1,
        sections: [
            {
                id: 1,
                title: "Introduction",
                order: 1,
                subsections: [
                    {
                        id: 1,
                        title: "Les merveilles de la première année",
                        order: 1,
                        paragraphs: [
                            {
                                id: 1,
                                content: "La première année de vie d'un bébé est remplie de moments magiques et de développements extraordinaires. Chaque mois apporte son lot de nouvelles compétences et de découvertes fascinantes, tant pour le bébé que pour les parents qui l'accompagnent dans cette aventure."
                            }
                        ]
                    }
                ]
            },
            {
                id: 2,
                title: "Les étapes du développement",
                order: 2,
                subsections: [
                    {
                        id: 2,
                        title: "0-3 mois : Le temps de l'adaptation",
                        order: 1,
                        paragraphs: [
                            {
                                id: 2,
                                content: "Durant les trois premiers mois, bébé s'adapte à son nouvel environnement. Il commence à reconnaître les visages, particulièrement celui de sa mère, et réagit aux sons familiers. Ses réflexes primitifs, comme la succion et l'agrippement, sont très présents. Vers la fin de cette période, les premiers sourires sociaux apparaissent, créant des moments de connexion profonde avec les parents."
                            }
                        ]
                    },
                    {
                        id: 3,
                        title: "4-6 mois : L'éveil aux sens",
                        order: 2,
                        paragraphs: [
                            {
                                id: 3,
                                content: "Entre 4 et 6 mois, bébé développe considérablement sa coordination. Il commence à tenir sa tête, à se retourner et à s'asseoir avec soutien. C'est aussi la période où il découvre ses mains et commence à saisir volontairement les objets. Son intérêt pour le monde extérieur grandit, et il devient plus expressif, riant aux éclats et babillant joyeusement."
                            }
                        ]
                    }
                ]
            }
        ],
        images: [
            {
                id: 1,
                url: "https://images.pexels.com/photos/3875220/pexels-photo-3875220.jpeg?auto=format&fit=crop&w=1034&h=432&q=80",
                caption: "Les étapes du développement de bébé",
                order: 1
            }
        ]
    },
    {
        id: 2,
        title: "Être maman : trouver l'équilibre entre maternité et bien-être personnel",
        author: "Marie Dubois",
        created_at: "2023-08-22T10:00:00Z",
        updated_at: "2023-08-22T10:00:00Z",
        category_id: 2,
        sections: [
            {
                id: 1,
                title: "Introduction",
                order: 1,
                subsections: [
                    {
                        id: 1,
                        title: "La maternité, un voyage transformateur",
                        order: 1,
                        paragraphs: [
                            {
                                id: 1,
                                content: "Devenir mère est l'une des expériences les plus transformatrices dans la vie d'une femme. Ce nouveau rôle apporte une joie immense, mais aussi des défis considérables. Trouver un équilibre entre prendre soin de son enfant et préserver son bien-être personnel est essentiel pour une maternité épanouie."
                            }
                        ]
                    }
                ]
            },
            {
                id: 2,
                title: "Prendre soin de soi",
                order: 2,
                subsections: [
                    {
                        id: 2,
                        title: "L'importance du self-care",
                        order: 1,
                        paragraphs: [
                            {
                                id: 2,
                                content: "Prendre du temps pour soi n'est pas un luxe mais une nécessité. Même quelques minutes par jour consacrées à une activité qui vous ressource peuvent faire une grande différence. Que ce soit lire un livre, prendre un bain, méditer ou simplement savourer une tasse de thé en paix, ces moments sont précieux pour recharger vos batteries et être plus présente pour votre enfant."
                            }
                        ]
                    },
                    {
                        id: 3,
                        title: "Créer un réseau de soutien",
                        order: 2,
                        paragraphs: [
                            {
                                id: 3,
                                content: "Aucune maman ne devrait se sentir seule dans son parcours. Entourez-vous de personnes bienveillantes qui peuvent vous offrir un soutien pratique et émotionnel. Rejoindre des groupes de mamans, partager vos expériences avec d'autres parents ou simplement accepter l'aide proposée par vos proches peut alléger considérablement votre charge mentale et physique."
                            }
                        ]
                    }
                ]
            }
        ],
        images: [
            {
                id: 1,
                url: "https://images.pexels.com/photos/3763583/pexels-photo-3763583.jpeg?auto=format&fit=crop&w=1034&h=432&q=80",
                caption: "L'équilibre entre maternité et bien-être personnel",
                order: 1
            }
        ]
    }
];


/**
 * Convertit les données Article en format compatible avec ArticleProps
 */
const convertArticleToProps = (article: ArticleType): any => {
    return {
        ...article,
        category_id: (article as any).category_id || 1,
        sections: article.sections.map(section => ({
            ...section,
            order: section.order || 1,
            subsections: section.subsections.map(subsection => ({
                ...subsection,
                order: subsection.order || 1,
                paragraphs: subsection.paragraphs
            }))
        })),
        images: article.images.map(image => ({
            ...image,
            order: image.order || 1
        }))
    };
};

/**
 * Composant Articles dynamique pour le frontoffice
 * Récupère les articles depuis l'API avec fallback sur les données statiques
 */
const Articles = React.memo(() => {
    const [articles, setArticles] = React.useState<ArticleType[]>([]);
    const [loading, setLoading] = React.useState(true);
    const [error, setError] = React.useState<string | null>(null);

    // Chargement des articles au montage du composant
    React.useEffect(() => {
        const loadArticles = async () => {
            try {
                setLoading(true);
                setError(null);

                // Tentative de récupération depuis l'API
                const fetchedArticles = await fetchPublishedArticles();

                if (fetchedArticles.length > 0) {
                    // Articles trouvés dans l'API
                    setArticles(fetchedArticles);
                    console.log('✅ Articles chargés depuis l\'API:', fetchedArticles.length);
                } else {
                    // Aucun article dans l'API, utiliser les données de fallback
                    setArticles(fallbackArticleData);
                    console.log('⚠️ Aucun article dans l\'API, utilisation des données de fallback');
                    console.log('💡 Pour poster les articles de base, exécutez: window.articleMigration.postBaseArticles()');
                }
            } catch (err) {
                // Erreur API, utiliser les données de fallback
                console.error('❌ Erreur lors du chargement des articles:', err);
                setError('Impossible de charger les articles depuis le serveur');
                setArticles(fallbackArticleData);
                console.log('🔄 Utilisation des données de fallback suite à l\'erreur');
            } finally {
                setLoading(false);
            }
        };

        loadArticles();
    }, []);

    // Affichage du loader pendant le chargement
    if (loading) {
        return (
            <div className="article-container-main">
                <div className="articles-container container">
                    <div className="articles-loading">
                        <div className="loading-spinner"></div>
                        <p>Chargement des articles...</p>
                    </div>
                </div>
            </div>
        );
    }

    // Affichage d'un message d'erreur si nécessaire (mais avec les données de fallback)
    const showErrorMessage = error && articles === fallbackArticleData;

    return (
        <div className="article-container-main">
            <div className="articles-container container">
                {showErrorMessage && (
                    <div className="articles-error-notice">
                        <p>⚠️ {error}. Affichage du contenu de démonstration.</p>
                    </div>
                )}

                {articles.length > 0 ? (
                    articles.map((articleData, index) => (
                        <Article
                            {...convertArticleToProps(articleData)}
                            key={articleData.id || index}
                        />
                    ))
                ) : (
                    <div className="articles-empty">
                        <p>Aucun article disponible pour le moment.</p>
                    </div>
                )}
            </div>
        </div>
    );
});

export default Articles;

import React from "react";
import ArticleForm from "../../../../../../utilities/minitiatures/ArticleForm/ArticleForm";
import { Article } from "../../../../../../utilities/constants/types";
import Button from "../../../../../../utilities/minitiatures/Button/Button";
import { Modal } from "react-bootstrap";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../../../../utilities/redux/store";
import { refreshArticles } from "../../../../../../utilities/redux/backoffice/backofficeSlice";
import Title from "../../../../../../utilities/minitiatures/Title/Title";

// Import des utilitaires de débogage (en développement uniquement)
if (import.meta.env.DEV) {
    import('../../../../../../utilities/helpers/debugArticleCreation');
    import('../../../../../../utilities/helpers/testFormData');
    import('../../../../../../utilities/helpers/testProductFormData');
    import('../../../../../../utilities/helpers/testFixedFormData');
    import('../../../../../../utilities/helpers/compareWithProducts');
    import('../../../../../../utilities/helpers/testDirectAPI');
    import('../../../../../../utilities/helpers/testBackendFormat');
    import('../../../../../../utilities/helpers/testNewFormat');
    import('../../../../../../utilities/helpers/testBlobConversion');
    import('../../../../../../utilities/helpers/testSectionFlattening');
}

/**
 * Composant pour l'ajout d'un nouvel article
 * Affiche un bouton qui ouvre un modal avec un formulaire
 * Utilise Redux pour envoyer les données à l'API
 */
const AddArticle = React.memo(() => {
    const dispatch = useDispatch<AppDispatch>();
    const submitRef = React.useRef<() => void>(null);

    const [state, setState] = React.useState({
        show: false,
        loading: false,
    });

    const setShow = React.useCallback((show: boolean) => setState(s => ({ ...s, show })), []);
    const setLoading = React.useCallback((loading: boolean) => setState(s => ({ ...s, loading })), []);

    const handleArticleSubmit = React.useCallback(async (articleData: Omit<Article, 'id' | 'images'> & { images: File[] }) => {
        try {
            setLoading(true);

            console.log('📤 Données article reçues:', {
                title: articleData.title,
                author: articleData.author,
                category_id: articleData.category_id,
                sectionsCount: articleData.sections.length,
                imagesCount: articleData.images.length
            });

            // 🔧 NOUVEAU: Transformer les images au format attendu par le backend
            const transformedImages = articleData.images.map((file, index) => ({
                id: Date.now() + index, // ID temporaire
                url: file, // Le backend attend un blob/File dans url
                caption: "", // Caption vide par défaut
                order: index + 1
            }));

            const payload = {
                title: articleData.title,
                author: articleData.author,
                category_id: articleData.category_id,
                sections: articleData.sections,
                images: transformedImages // Format backend attendu
            };

            const { createArticle } = await import('../../../../../../utilities/api/actions');
            await createArticle(payload);

            // Rafraîchir la liste des articles
            dispatch(refreshArticles());

            // Fermer le modal
            setShow(false);

            // Afficher un message de succès
            alert("Article créé avec succès !");
        } catch (error) {
            console.error("Erreur lors de la création de l'article:", error);
            alert("Erreur lors de la création de l'article. Veuillez réessayer.");
        } finally {
            setLoading(false);
        }
    }, [dispatch, setShow, setLoading]);

    const handleSubmitClick = React.useCallback(() => {
        if (submitRef.current) {
            submitRef.current();
        }
    }, []);

    return (
        <div className="add-article-container">
            <Button
                className="btn btn-secondary add-button"
                type="button"
                onClick={() => setShow(true)}
            >
                <i className="fa fa-plus"></i> Créer
            </Button>

            <Modal show={state.show} onHide={() => setShow(false)} size="xl" centered>
                <Modal.Header closeButton>
                    <Modal.Title>
                        <Title text="Création d'un article" />
                        <small className="text-muted d-block">
                            Remplir les données pour enregistrer un nouvel article
                        </small>
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <ArticleForm
                        onSubmit={handleArticleSubmit}
                        submitRef={submitRef}
                    />
                </Modal.Body>
                <Modal.Footer>
                    <Button
                        type="button"
                        className="btn btn-outline-dark btn-sm"
                        onClick={() => setShow(false)}
                    >
                        Annuler
                    </Button>
                    <Button
                        type="button"
                        className="btn btn-primary"
                        onClick={handleSubmitClick}
                        options={{ loading: state.loading }}
                    >
                        <i className="fa fa-check"></i> Enregistrer
                    </Button>
                </Modal.Footer>
            </Modal>
        </div>
    );
});

export default AddArticle;

/**
 * Test pour comparer le FormData des produits vs articles
 */

import toFormData from './toFormData';

export const testProductFormData = () => {
    console.log('🧪 Test FormData pour produits (référence)...');
    
    // Créer des fichiers simulés comme pour les produits
    const mockImage1 = new File(['image content 1'], 'product1.jpg', { type: 'image/jpeg' });
    const mockImage2 = new File(['image content 2'], 'product2.png', { type: 'image/png' });

    const productData = {
        title: "Test Product",
        description: "Description du produit",
        price: 100,
        category_id: 1,
        images: [mockImage1, mockImage2]
    };

    try {
        const formData = toFormData(productData);
        
        console.log('✅ FormData produit créé avec succès');
        console.log('📋 Contenu FormData produit:');
        
        for (let [key, value] of formData.entries()) {
            if (value instanceof File) {
                console.log(`  ${key}: File(${value.name}, ${value.size} bytes)`);
            } else {
                console.log(`  ${key}:`, value);
            }
        }
        
        return formData;
    } catch (error) {
        console.error('❌ Erreur FormData produit:', error);
        return null;
    }
};

export const compareFormDataStructures = () => {
    console.log('🔍 Comparaison des structures FormData...');
    
    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    
    // Test avec différentes structures
    const structures = {
        'images[]': () => {
            const form = new FormData();
            form.append('title', 'Test');
            form.append('images[]', mockFile);
            return form;
        },
        'images[0]': () => {
            const form = new FormData();
            form.append('title', 'Test');
            form.append('images[0]', mockFile);
            return form;
        },
        'images': () => {
            const form = new FormData();
            form.append('title', 'Test');
            form.append('images', mockFile);
            return form;
        }
    };

    Object.entries(structures).forEach(([name, createForm]) => {
        console.log(`\n📋 Structure "${name}":`);
        const form = createForm();
        for (let [key, value] of form.entries()) {
            if (value instanceof File) {
                console.log(`  ${key}: File(${value.name})`);
            } else {
                console.log(`  ${key}: ${value}`);
            }
        }
    });
};

// Exposer globalement
if (typeof window !== 'undefined') {
    (window as any).testProductFormData = {
        testProductFormData,
        compareFormDataStructures
    };
}

# Script pour redémarrer le serveur de développement avec cache propre

Write-Host "🔄 Arrêt des processus Node/Vite..." -ForegroundColor Yellow
Get-Process | Where-Object {$_.ProcessName -like "*node*" -or $_.ProcessName -like "*vite*"} | Stop-Process -Force -ErrorAction SilentlyContinue

Write-Host "🧹 Nettoyage du cache Vite..." -ForegroundColor Yellow
Remove-Item -Path "node_modules/.vite" -Recurse -Force -ErrorAction SilentlyContinue

Write-Host "⏳ Attente de 2 secondes..." -ForegroundColor Yellow
Start-Sleep -Seconds 2

Write-Host "🚀 Redémarrage du serveur de développement..." -ForegroundColor Green
npm run dev

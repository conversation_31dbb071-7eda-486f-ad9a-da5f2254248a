/**
 * Test de comparaison directe Products vs Articles
 */

import { createProduct, createArticle } from '../api/actions';

export const testProductCreation = async () => {
    console.log('🧪 Test création PRODUIT (référence qui fonctionne)...');

    const mockImage1 = new File(['product content 1'], 'product1.jpg', { type: 'image/jpeg' });
    const mockImage2 = new File(['product content 2'], 'product2.png', { type: 'image/png' });

    const productPayload = {
        title: "Test Product Direct",
        description: "Description test",
        price: 5000,  // ✅ Prix minimum requis
        inStock: 10,  // ✅ Stock requis
        category_id: 1,
        images: [mockImage1, mockImage2]  // File[] directement
    };

    console.log('📤 Payload produit:', productPayload);

    try {
        const response = await createProduct(productPayload);
        console.log('✅ PRODUIT créé avec succès:', response.data);
        return { success: true, data: response.data };
    } catch (error: any) {
        console.error('❌ PRODUIT échoué:', error);
        if (error.response) {
            console.error('📋 Détails erreur produit:', {
                status: error.response.status,
                data: error.response.data
            });
        }
        return { success: false, error: error.response?.data };
    }
};

export const testArticleCreationSimple = async () => {
    console.log('🧪 Test création ARTICLE (même structure que produit)...');

    const mockImage1 = new File(['article content 1'], 'article1.jpg', { type: 'image/jpeg' });
    const mockImage2 = new File(['article content 2'], 'article2.png', { type: 'image/png' });

    const articlePayload = {
        title: "Test Article Direct",
        author: "Test Author",
        category_id: 1,
        sections: [],  // Vide pour simplifier
        images: [mockImage1, mockImage2]  // File[] directement comme produit
    };

    console.log('📤 Payload article:', articlePayload);

    try {
        const response = await createArticle(articlePayload);
        console.log('✅ ARTICLE créé avec succès:', response.data);
        return { success: true, data: response.data };
    } catch (error: any) {
        console.error('❌ ARTICLE échoué:', error);
        if (error.response) {
            console.error('📋 Détails erreur article:', {
                status: error.response.status,
                data: error.response.data
            });
        }
        return { success: false, error: error.response?.data };
    }
};

export const compareProductVsArticle = async () => {
    console.log('🔍 COMPARAISON DIRECTE Products vs Articles...');

    console.log('\n=== TEST PRODUIT ===');
    const productResult = await testProductCreation();

    console.log('\n=== TEST ARTICLE ===');
    const articleResult = await testArticleCreationSimple();

    console.log('\n=== RÉSULTATS COMPARAISON ===');
    console.log(`Produit: ${productResult.success ? '✅ SUCCÈS' : '❌ ÉCHEC'}`);
    console.log(`Article: ${articleResult.success ? '✅ SUCCÈS' : '❌ ÉCHEC'}`);

    if (!productResult.success && !articleResult.success) {
        console.log('🚨 LES DEUX ÉCHOUENT - Problème général');
    } else if (productResult.success && !articleResult.success) {
        console.log('🎯 PRODUIT OK, ARTICLE KO - Problème spécifique backend Articles');
        console.log('Erreur article:', articleResult.error);
    } else if (!productResult.success && articleResult.success) {
        console.log('🤔 ARTICLE OK, PRODUIT KO - Inattendu');
    } else {
        console.log('✅ LES DEUX FONCTIONNENT - Problème résolu');
    }

    return { productResult, articleResult };
};

// Exposer globalement
if (typeof window !== 'undefined') {
    (window as any).testComparison = {
        testProductCreation,
        testArticleCreationSimple,
        compareProductVsArticle
    };

    console.log('🔧 Tests comparaison disponibles:');
    console.log('  window.testComparison.testProductCreation()');
    console.log('  window.testComparison.testArticleCreationSimple()');
    console.log('  window.testComparison.compareProductVsArticle()');
}

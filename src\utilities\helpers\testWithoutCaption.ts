/**
 * Test sans le champ caption qui pose problème
 */

import { createArticle } from '../api/actions';

// Créer un blob d'image valide
const createImageBlob = (name: string): File => {
    const pngData = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==';
    const byteCharacters = atob(pngData);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: 'image/png' });
    return new File([blob], name, { type: 'image/png' });
};

export const testArticleWithoutCaption = async () => {
    console.log('🧪 Test article SANS caption (qui pose problème)...');
    
    const imageFile = createImageBlob('no-caption.png');

    const payload = {
        title: "Test Article Sans Caption",
        author: "Test Author",
        category_id: 1,
        sections: [],
        images: [
            {
                id: 1,
                url: imageFile,
                // caption: "",  // ❌ Retiré car pose problème
                order: 1
            }
        ]
    };

    console.log('📤 Payload sans caption:', {
        title: payload.title,
        author: payload.author,
        category_id: payload.category_id,
        sectionsCount: payload.sections.length,
        imagesCount: payload.images.length,
        imageStructure: payload.images.map(img => ({
            id: img.id,
            urlType: typeof img.url,
            // caption: img.caption,  // Pas de caption
            order: img.order
        }))
    });

    try {
        const response = await createArticle(payload);
        console.log('🎉 SUCCÈS SANS CAPTION - PROBLÈME RÉSOLU !', response.data);
        return { success: true, data: response.data };
    } catch (error: any) {
        console.error('❌ Échec même sans caption:', error);
        if (error.response) {
            console.error('📋 Détails:', {
                status: error.response.status,
                data: error.response.data
            });
        }
        return { success: false, error: error.response?.data };
    }
};

export const testArticleWithEmptyCaption = async () => {
    console.log('🧪 Test article avec caption VIDE...');
    
    const imageFile = createImageBlob('empty-caption.png');

    const payload = {
        title: "Test Article Caption Vide",
        author: "Test Author",
        category_id: 1,
        sections: [],
        images: [
            {
                id: 1,
                url: imageFile,
                caption: "",  // Caption vide
                order: 1
            }
        ]
    };

    try {
        const response = await createArticle(payload);
        console.log('✅ SUCCÈS avec caption vide:', response.data);
        return { success: true, data: response.data };
    } catch (error: any) {
        console.error('❌ Échec avec caption vide:', error);
        if (error.response) {
            console.error('📋 Détails:', error.response.data);
        }
        return { success: false, error: error.response?.data };
    }
};

export const testBothCaptionVariants = async () => {
    console.log('🔍 TEST VARIANTS CAPTION...');
    
    console.log('\n=== SANS CAPTION ===');
    const noCaptionResult = await testArticleWithoutCaption();
    
    console.log('\n=== CAPTION VIDE ===');
    const emptyCaptionResult = await testArticleWithEmptyCaption();
    
    console.log('\n=== RÉSULTATS CAPTION ===');
    console.log(`Sans caption: ${noCaptionResult.success ? '✅ SUCCÈS' : '❌ ÉCHEC'}`);
    console.log(`Caption vide: ${emptyCaptionResult.success ? '✅ SUCCÈS' : '❌ ÉCHEC'}`);
    
    if (noCaptionResult.success || emptyCaptionResult.success) {
        console.log('🎉 PROBLÈME RÉSOLU - Articles fonctionnent !');
        console.log('💡 Solution : Éviter le champ caption ou le configurer côté backend');
    } else {
        console.log('❌ Problème persiste même sans caption');
    }
    
    return { noCaptionResult, emptyCaptionResult };
};

// Exposer globalement
if (typeof window !== 'undefined') {
    (window as any).testNoCaption = {
        testArticleWithoutCaption,
        testArticleWithEmptyCaption,
        testBothCaptionVariants
    };
    
    console.log('🔧 Tests sans caption disponibles:');
    console.log('  window.testNoCaption.testArticleWithoutCaption()');
    console.log('  window.testNoCaption.testArticleWithEmptyCaption()');
    console.log('  window.testNoCaption.testBothCaptionVariants()');
}

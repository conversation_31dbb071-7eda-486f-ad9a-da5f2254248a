/**
 * Comparaison détaillée entre Articles et Products pour identifier les différences
 */

import toFormData from './toFormData';
import { createProduct } from '../api/actions';

export const testProductImageCreation = async () => {
    console.log('🧪 Test de création de produit avec images (référence)...');
    
    const mockImage1 = new File(['image content 1'], 'product1.jpg', { type: 'image/jpeg' });
    const mockImage2 = new File(['image content 2'], 'product2.png', { type: 'image/png' });

    const productData = {
        title: "Test Product avec images",
        description: "Description test",
        price: 100,
        category_id: 1,
        images: [mockImage1, mockImage2]
    };

    console.log('📤 Données produit à envoyer:', {
        title: productData.title,
        description: productData.description,
        price: productData.price,
        category_id: productData.category_id,
        imagesCount: productData.images.length
    });

    try {
        // Test direct de l'API produit
        const response = await createProduct(productData);
        console.log('✅ Produit avec images créé avec succès:', response.data);
        return response.data;
    } catch (error: any) {
        console.error('❌ Erreur création produit:', error);
        if (error.response) {
            console.error('📋 Détails erreur produit:', {
                status: error.response.status,
                data: error.response.data
            });
        }
        return null;
    }
};

export const compareFormDataStructures = () => {
    console.log('🔍 Comparaison structures FormData Articles vs Products...');
    
    const mockImage = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    
    // Structure Article
    const articleData = {
        title: "Test Article",
        author: "Test Author",
        category_id: 1,
        sections: [],
        images: [mockImage]
    };
    
    // Structure Product
    const productData = {
        title: "Test Product",
        description: "Test Description",
        price: 100,
        category_id: 1,
        images: [mockImage]
    };

    console.log('\n📋 FormData Article:');
    const articleFormData = toFormData(articleData);
    for (let [key, value] of articleFormData.entries()) {
        if (value instanceof File) {
            console.log(`  ${key}: File(${value.name})`);
        } else {
            console.log(`  ${key}: ${value}`);
        }
    }

    console.log('\n📋 FormData Product:');
    const productFormData = toFormData(productData);
    for (let [key, value] of productFormData.entries()) {
        if (value instanceof File) {
            console.log(`  ${key}: File(${value.name})`);
        } else {
            console.log(`  ${key}: ${value}`);
        }
    }

    // Comparaison des clés
    const articleKeys = Array.from(articleFormData.keys());
    const productKeys = Array.from(productFormData.keys());
    
    console.log('\n🔍 Comparaison des clés:');
    console.log('Article keys:', articleKeys);
    console.log('Product keys:', productKeys);
    
    const uniqueToArticle = articleKeys.filter(k => !productKeys.includes(k));
    const uniqueToProduct = productKeys.filter(k => !articleKeys.includes(k));
    
    console.log('Unique à Article:', uniqueToArticle);
    console.log('Unique à Product:', uniqueToProduct);
};

export const testEndpointDifferences = async () => {
    console.log('🔍 Test des différences d\'endpoints...');
    
    // Test endpoint produit
    console.log('\n📡 Test endpoint /product/create...');
    try {
        const productResponse = await fetch('http://localhost:5173/api/product/create', {
            method: 'OPTIONS'
        });
        console.log('Product endpoint status:', productResponse.status);
    } catch (error) {
        console.error('Erreur endpoint product:', error);
    }
    
    // Test endpoint article
    console.log('\n📡 Test endpoint /article/create...');
    try {
        const articleResponse = await fetch('http://localhost:5173/api/article/create', {
            method: 'OPTIONS'
        });
        console.log('Article endpoint status:', articleResponse.status);
    } catch (error) {
        console.error('Erreur endpoint article:', error);
    }
};

// Exposer globalement
if (typeof window !== 'undefined') {
    (window as any).compareWithProducts = {
        testProductImageCreation,
        compareFormDataStructures,
        testEndpointDifferences
    };
    
    console.log('🔧 Fonctions de comparaison disponibles:');
    console.log('  window.compareWithProducts.testProductImageCreation()');
    console.log('  window.compareWithProducts.compareFormDataStructures()');
    console.log('  window.compareWithProducts.testEndpointDifferences()');
}

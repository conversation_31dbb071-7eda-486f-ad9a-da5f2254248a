/**
 * Test de l'aplatissement des sections pour FormData
 */

import toFormData from './toFormData';

export const testSectionFlattening = () => {
    console.log('🧪 Test aplatissement des sections...');
    
    const testData = {
        title: "Test Sections",
        author: "Test Author",
        category_id: 1,
        sections: [
            {
                id: 1,
                title: "Section 1",
                order: 1,
                subsections: [
                    {
                        id: 1,
                        title: "Subsection 1",
                        order: 1,
                        paragraphs: [
                            {
                                id: 1,
                                content: "Contenu paragraphe 1"
                            }
                        ]
                    }
                ]
            }
        ],
        images: []
    };

    try {
        const formData = toFormData(testData);
        
        console.log('✅ FormData avec sections aplaties créé');
        console.log('📋 Contenu FormData:');
        
        const entries = [];
        for (let [key, value] of formData.entries()) {
            entries.push({ key, value });
            console.log(`  ${key}: ${value}`);
        }
        
        // Vérifier les champs requis
        const hasTitle = entries.some(e => e.key.includes('sections[0]') && e.key.includes('title'));
        const hasOrder = entries.some(e => e.key.includes('sections[0]') && e.key.includes('order'));
        
        console.log('\n🔍 Vérification des champs requis:');
        console.log(`  sections[0].title présent: ${hasTitle ? '✅' : '❌'}`);
        console.log(`  sections[0].order présent: ${hasOrder ? '✅' : '❌'}`);
        
        if (hasTitle && hasOrder) {
            console.log('✅ Tous les champs requis sont présents');
        } else {
            console.log('❌ Champs requis manquants');
        }
        
        return formData;
    } catch (error) {
        console.error('❌ Erreur aplatissement sections:', error);
        return null;
    }
};

export const testMinimalSection = () => {
    console.log('🧪 Test section minimale...');
    
    const testData = {
        title: "Test Minimal",
        author: "Test Author",
        category_id: 1,
        sections: [
            {
                id: 1,
                title: "Section Simple",
                order: 1,
                subsections: []
            }
        ],
        images: []
    };

    try {
        const formData = toFormData(testData);
        
        console.log('✅ FormData section minimale créé');
        console.log('📋 Contenu:');
        
        for (let [key, value] of formData.entries()) {
            console.log(`  ${key}: ${value}`);
        }
        
        return formData;
    } catch (error) {
        console.error('❌ Erreur section minimale:', error);
        return null;
    }
};

export const testEmptySections = () => {
    console.log('🧪 Test sections vides...');
    
    const testData = {
        title: "Test Vide",
        author: "Test Author",
        category_id: 1,
        sections: [],
        images: []
    };

    try {
        const formData = toFormData(testData);
        
        console.log('✅ FormData sections vides créé');
        console.log('📋 Contenu:');
        
        for (let [key, value] of formData.entries()) {
            console.log(`  ${key}: ${value}`);
        }
        
        return formData;
    } catch (error) {
        console.error('❌ Erreur sections vides:', error);
        return null;
    }
};

// Exposer globalement
if (typeof window !== 'undefined') {
    (window as any).testSections = {
        testSectionFlattening,
        testMinimalSection,
        testEmptySections
    };
    
    console.log('🔧 Tests sections disponibles:');
    console.log('  window.testSections.testSectionFlattening()');
    console.log('  window.testSections.testMinimalSection()');
    console.log('  window.testSections.testEmptySections()');
}

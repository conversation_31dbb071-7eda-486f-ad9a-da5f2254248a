type Payload = {
    [key: string]: any
}

export default function (payload: Payload): FormData {
    const form = new FormData();

    const keys = Object.keys(payload);

    keys.length > 0 && keys.forEach(key => {
        const value = payload[key];

        if (Array.isArray(value)) {
            // Pour les tableaux, on utilise la notation []
            value.forEach((val: any, index: number) => {
                if (val instanceof File) {
                    // Pour les fichiers, on utilise directement la clé avec index
                    form.append(`${key}[${index}]`, val);
                } else if (typeof val === 'object' && val !== null) {
                    // Pour les objets complexes, on les sérialise en JSON
                    form.append(`${key}[${index}]`, JSON.stringify(val));
                } else {
                    // Pour les valeurs primitives
                    form.append(`${key}[${index}]`, val);
                }
            });
        } else if (value instanceof File) {
            // Pour un fichier unique
            form.append(key, value);
        } else if (typeof value === 'object' && value !== null) {
            // Pour les objets, on les sérialise en JSON
            form.append(key, JSON.stringify(value));
        } else if (value !== undefined && value !== null) {
            // Pour les valeurs primitives (string, number, boolean)
            form.append(key, String(value));
        }
    });

    return form;
}
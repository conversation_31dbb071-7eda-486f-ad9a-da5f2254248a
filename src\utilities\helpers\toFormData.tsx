type Payload = {
    [key: string]: any
}

export default function (payload: Payload): FormData {
    const form = new FormData();

    const keys = Object.keys(payload);

    keys.length > 0 && keys.forEach(key => {
        const value = payload[key];

        if (Array.isArray(value)) {
            // Pour les tableaux, on utilise la notation []
            value.forEach((val: any, index: number) => {
                if (val instanceof File) {
                    // Pour les fichiers, Laravel s'attend à la notation images[]
                    form.append(`${key}[]`, val);
                } else if (typeof val === 'object' && val !== null) {
                    // Pour les images avec structure backend {id, url: File, caption, order}
                    if (key === 'images' && 'url' in val && val.url instanceof File) {
                        // Structure backend pour images
                        form.append(`${key}[${index}][id]`, String(val.id));
                        form.append(`${key}[${index}][url]`, val.url); // File directement
                        form.append(`${key}[${index}][caption]`, String(val.caption));
                        form.append(`${key}[${index}][order]`, String(val.order));
                    }
                    // Pour les sections, aplatir la structure pour Laravel
                    else if (key === 'sections') {
                        // Aplatir chaque section
                        Object.keys(val).forEach(sectionKey => {
                            const sectionValue = val[sectionKey];
                            if (Array.isArray(sectionValue)) {
                                // Pour les sous-tableaux (subsections, paragraphs)
                                sectionValue.forEach((subVal: any, subIndex: number) => {
                                    if (typeof subVal === 'object' && subVal !== null) {
                                        Object.keys(subVal).forEach(subKey => {
                                            const subValue = subVal[subKey];
                                            if (Array.isArray(subValue)) {
                                                // Pour les paragraphes
                                                subValue.forEach((paraVal: any, paraIndex: number) => {
                                                    if (typeof paraVal === 'object' && paraVal !== null) {
                                                        Object.keys(paraVal).forEach(paraKey => {
                                                            form.append(`${key}[${index}][${sectionKey}][${subIndex}][${subKey}][${paraIndex}][${paraKey}]`, String(paraVal[paraKey]));
                                                        });
                                                    } else {
                                                        form.append(`${key}[${index}][${sectionKey}][${subIndex}][${subKey}][${paraIndex}]`, String(paraVal));
                                                    }
                                                });
                                            } else {
                                                form.append(`${key}[${index}][${sectionKey}][${subIndex}][${subKey}]`, String(subValue));
                                            }
                                        });
                                    } else {
                                        form.append(`${key}[${index}][${sectionKey}][${subIndex}]`, String(subVal));
                                    }
                                });
                            } else {
                                form.append(`${key}[${index}][${sectionKey}]`, String(sectionValue));
                            }
                        });
                    } else {
                        // Pour les autres objets complexes, sérialiser en JSON
                        form.append(`${key}[${index}]`, JSON.stringify(val));
                    }
                } else {
                    // Pour les valeurs primitives
                    form.append(`${key}[${index}]`, val);
                }
            });
        } else if (value instanceof File) {
            // Pour un fichier unique
            form.append(key, value);
        } else if (typeof value === 'object' && value !== null) {
            // Pour les objets, on les sérialise en JSON
            form.append(key, JSON.stringify(value));
        } else if (value !== undefined && value !== null) {
            // Pour les valeurs primitives (string, number, boolean)
            form.append(key, String(value));
        }
    });

    return form;
}
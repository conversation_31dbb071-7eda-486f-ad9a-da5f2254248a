/**
 * Test sans images pour isoler le problème
 */

import { createProduct, createArticle } from '../api/actions';

export const testProductWithoutImages = async () => {
    console.log('🧪 Test PRODUIT sans images...');
    
    const productPayload = {
        title: "Test Product Sans Images",
        description: "Description test",
        price: 5000,
        inStock: 10,
        category_id: 1,
        images: []  // Pas d'images
    };

    console.log('📤 Payload produit sans images:', productPayload);

    try {
        const response = await createProduct(productPayload);
        console.log('✅ PRODUIT sans images créé:', response.data);
        return { success: true, data: response.data };
    } catch (error: any) {
        console.error('❌ PRODUIT sans images échoué:', error);
        if (error.response) {
            console.error('📋 Détails:', {
                status: error.response.status,
                data: error.response.data
            });
        }
        return { success: false, error: error.response?.data };
    }
};

export const testArticleWithoutImages = async () => {
    console.log('🧪 Test ARTICLE sans images...');
    
    const articlePayload = {
        title: "Test Article Sans Images",
        author: "Test Author",
        category_id: 1,
        sections: [],
        images: []  // Pas d'images
    };

    console.log('📤 Payload article sans images:', articlePayload);

    try {
        const response = await createArticle(articlePayload);
        console.log('✅ ARTICLE sans images créé:', response.data);
        return { success: true, data: response.data };
    } catch (error: any) {
        console.error('❌ ARTICLE sans images échoué:', error);
        if (error.response) {
            console.error('📋 Détails:', {
                status: error.response.status,
                data: error.response.data
            });
        }
        return { success: false, error: error.response?.data };
    }
};

export const testBothWithoutImages = async () => {
    console.log('🔍 TEST SANS IMAGES - Products vs Articles...');
    
    console.log('\n=== PRODUIT SANS IMAGES ===');
    const productResult = await testProductWithoutImages();
    
    console.log('\n=== ARTICLE SANS IMAGES ===');
    const articleResult = await testArticleWithoutImages();
    
    console.log('\n=== RÉSULTATS SANS IMAGES ===');
    console.log(`Produit sans images: ${productResult.success ? '✅ SUCCÈS' : '❌ ÉCHEC'}`);
    console.log(`Article sans images: ${articleResult.success ? '✅ SUCCÈS' : '❌ ÉCHEC'}`);
    
    if (productResult.success && articleResult.success) {
        console.log('🎯 LES DEUX FONCTIONNENT SANS IMAGES - Le problème vient des images');
    } else if (productResult.success && !articleResult.success) {
        console.log('🚨 PRODUIT OK, ARTICLE KO même sans images - Problème backend Articles général');
    } else if (!productResult.success && articleResult.success) {
        console.log('🤔 ARTICLE OK, PRODUIT KO - Problème validation produit');
    } else {
        console.log('❌ LES DEUX ÉCHOUENT même sans images - Problème général');
    }
    
    return { productResult, articleResult };
};

// Exposer globalement
if (typeof window !== 'undefined') {
    (window as any).testNoImages = {
        testProductWithoutImages,
        testArticleWithoutImages,
        testBothWithoutImages
    };
    
    console.log('🔧 Tests sans images disponibles:');
    console.log('  window.testNoImages.testProductWithoutImages()');
    console.log('  window.testNoImages.testArticleWithoutImages()');
    console.log('  window.testNoImages.testBothWithoutImages()');
}

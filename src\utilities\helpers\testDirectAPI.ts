/**
 * Test de l'approche directe API (comme les Products)
 */

import { createArticle } from '../api/actions';

export const testDirectArticleCreation = async () => {
    console.log('🧪 Test création article - Approche directe API...');
    
    const mockImage1 = new File(['image content 1'], 'direct1.jpg', { type: 'image/jpeg' });
    const mockImage2 = new File(['image content 2'], 'direct2.png', { type: 'image/png' });

    // Payload simple comme les Products
    const payload = {
        title: "Article Direct API",
        author: "Test Author Direct",
        category_id: 1,
        sections: [
            {
                id: Date.now(),
                title: "Section test direct",
                order: 1,
                subsections: [
                    {
                        id: Date.now() + 1,
                        title: "Subsection test",
                        order: 1,
                        paragraphs: [
                            {
                                id: Date.now() + 2,
                                content: "Contenu test direct"
                            }
                        ]
                    }
                ]
            }
        ],
        images: [mockImage1, mockImage2]
    };

    console.log('📤 Payload direct API:', {
        title: payload.title,
        author: payload.author,
        category_id: payload.category_id,
        sectionsCount: payload.sections.length,
        imagesCount: payload.images.length
    });

    try {
        const response = await createArticle(payload);
        console.log('✅ Article créé avec succès (API directe):', response.data);
        return response.data;
    } catch (error: any) {
        console.error('❌ Erreur API directe:', error);
        if (error.response) {
            console.error('📋 Détails erreur API directe:', {
                status: error.response.status,
                data: error.response.data
            });
        }
        return null;
    }
};

export const testDirectArticleWithoutSections = async () => {
    console.log('🧪 Test création article sans sections - API directe...');
    
    const mockImage = new File(['image content'], 'nosections.jpg', { type: 'image/jpeg' });

    // Payload minimal
    const payload = {
        title: "Article Sans Sections",
        author: "Test Author",
        category_id: 1,
        sections: [], // Vide
        images: [mockImage]
    };

    try {
        const response = await createArticle(payload);
        console.log('✅ Article sans sections créé:', response.data);
        return response.data;
    } catch (error: any) {
        console.error('❌ Erreur article sans sections:', error);
        if (error.response) {
            console.error('📋 Détails:', error.response.data);
        }
        return null;
    }
};

export const testDirectArticleJSONOnly = async () => {
    console.log('🧪 Test création article JSON seulement...');
    
    // Payload sans images (JSON pur)
    const payload = {
        title: "Article JSON Only",
        author: "Test Author JSON",
        category_id: 1,
        sections: [
            {
                id: Date.now(),
                title: "Section JSON",
                order: 1,
                subsections: []
            }
        ],
        images: [] // Pas d'images
    };

    try {
        const response = await createArticle(payload);
        console.log('✅ Article JSON créé:', response.data);
        return response.data;
    } catch (error: any) {
        console.error('❌ Erreur article JSON:', error);
        if (error.response) {
            console.error('📋 Détails:', error.response.data);
        }
        return null;
    }
};

// Exposer globalement
if (typeof window !== 'undefined') {
    (window as any).testDirectAPI = {
        testDirectArticleCreation,
        testDirectArticleWithoutSections,
        testDirectArticleJSONOnly
    };
    
    console.log('🔧 Tests API directe disponibles:');
    console.log('  window.testDirectAPI.testDirectArticleCreation()');
    console.log('  window.testDirectAPI.testDirectArticleWithoutSections()');
    console.log('  window.testDirectAPI.testDirectArticleJSONOnly()');
}

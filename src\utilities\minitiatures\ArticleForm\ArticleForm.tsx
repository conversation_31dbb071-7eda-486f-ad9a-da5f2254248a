import React, { useState } from 'react';
import { Article, Section, Category } from '../../constants/types';
import SectionForm from '../SectionForm/SectionForm';
import AddImages from '../../../App/Backoffice/Dashboard/Main/Products/AddImages/AddImages';
import SelectedCategory from '../../../App/Backoffice/Dashboard/Main/Categories/AddCategory/SelectedCategory/SelectedCategory';
import useCategorySelect from '../CategorySelect/hooks/useCategorySelect';
import Button from '../Button/Button';
import { Image } from '../ImageInputDD/ImageInputDD';

interface ArticleFormProps {
  onSubmit: (article: Omit<Article, 'id' | 'images'> & { images: File[] }) => void;
  initialData?: Article;
  submitRef?: React.RefObject<() => void>;
}

const ArticleForm: React.FC<ArticleFormProps> = ({ onSubmit, initialData, submitRef }) => {
  const [title, setTitle] = useState(initialData?.title || '');
  const [author, setAuthor] = useState(initialData?.author || '');
  const [category, setCategory] = useState<Category | null>(null);
  const [sections, setSections] = useState<Section[]>(initialData?.sections || []);
  const [images, setImages] = useState<Image[]>([]);

  const categorySelect = useCategorySelect();

  const handleAddSection = () => {
    setSections([...sections, { id: Date.now(), title: '', subsections: [] }]);
  };

  const handleRemoveSection = (id: number) => {
    setSections(sections.filter(section => section.id !== id));
  };

  const handleAddImage = React.useCallback((image: Image) => {
    setImages(prev => [...prev, image]);
  }, []);

  const handleRemoveImage = React.useCallback((url: string) => {
    setImages(prev => prev.filter(image => image.imageUrl !== url));
  }, []);

  const handleCategorySelectClose = React.useCallback((selected: Category | null) => {
    setCategory(selected);
  }, []);

  const handleOpenCategorySelect = React.useCallback(() => {
    categorySelect.open(handleCategorySelectClose, category?.id);
  }, [categorySelect, handleCategorySelectClose, category]);

  const handleSubmit = React.useCallback(() => {
    // Validation basique
    if (!title.trim()) {
      alert('Le titre est requis');
      return;
    }
    if (!author.trim()) {
      alert('L\'auteur est requis');
      return;
    }

    // Extraire les fichiers d'images
    const imageFiles = images
      .filter(img => img.imageData)
      .map(img => img.imageData as File);

    onSubmit({
      title: title.trim(),
      author: author.trim(),
      category_id: category?.id || null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      sections,
      images: imageFiles
    });
  }, [title, author, category, sections, images, onSubmit]);

  // Exposer la fonction de soumission via ref
  React.useImperativeHandle(submitRef, () => handleSubmit, [handleSubmit]);

  return (
    <div className="d-flex flex-wrap justify-content-between add-product-modal-body px-5">
      {/* Informations de base */}
      <div className="col-5 my-3">
        <label htmlFor="article-title">Titre de l'article *</label>
        <input
          type="text"
          className="form-control"
          id="article-title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Entrez le titre de l'article..."
        />
      </div>
      <div className="col-5 my-3">
        <label htmlFor="article-author">Auteur *</label>
        <input
          type="text"
          className="form-control"
          id="article-author"
          value={author}
          onChange={(e) => setAuthor(e.target.value)}
          placeholder="Nom de l'auteur..."
        />
      </div>

      {/* Catégorie */}
      <div className="col-5 my-3 d-flex justify-content-between">
        <div>
          <h6>Catégorie de l'article *</h6>
          <SelectedCategory category={category} />
        </div>
        <Button
          type="button"
          className="btn btn-outline-dark btn-sm align-self-start"
          onClick={handleOpenCategorySelect}>
          Ouvrir <i className="fa fa-external-link"></i>
        </Button>
      </div>

      {/* Images */}
      <div className="col-8 my-3">
        <h6>
          Images de l'article
        </h6>
        <AddImages
          addImage={handleAddImage}
          removeImage={handleRemoveImage}
          images={images}
          count={6}
        />
      </div>

      {/* Sections */}
      <div className="col-12 mb-4">
        <div className="d-flex justify-content-between align-items-center mb-3">
          <h6 className="mb-0">Sections du contenu</h6>
          <button
            type="button"
            className="btn btn-outline-primary btn-sm"
            onClick={handleAddSection}
          >
            <i className='fa fa-plus'></i> Ajouter une section
          </button>
        </div>
        {sections.length === 0 && (
          <div className="alert alert-info">
            <i className="fa fa-info-circle me-2"></i>
            Aucune section ajoutée. Cliquez sur "Ajouter une section" pour commencer.
          </div>
        )}
        {sections.map((section, index) => (
          <SectionForm
            key={section.id}
            section={section}
            onChange={(updatedSection) => {
              const newSections = [...sections];
              newSections[index] = updatedSection;
              setSections(newSections);
            }}
            onRemove={() => handleRemoveSection(section.id)}
          />
        ))}
      </div>
    </div>
  );
};

export default ArticleForm;

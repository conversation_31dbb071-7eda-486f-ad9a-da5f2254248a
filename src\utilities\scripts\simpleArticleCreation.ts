/**
 * Script simplifié pour créer des articles avec gestion d'erreurs robuste
 */

import { getAllCategories } from '../api/admin/actions';
import { createArticle } from '../api/actions';

// Articles avec structure simple
const testArticles = [
  {
    title: "Le développement de bébé : les étapes clés de la première année",
    content: "## Introduction\n\nLa première année de vie de votre bébé est marquée par des transformations extraordinaires. Chaque mois apporte son lot de nouvelles découvertes et d'acquisitions qui témoignent du développement rapide de votre petit trésor.\n\n## Les premiers mois : l'éveil sensoriel\n\n### 0-3 mois : Les fondations\nDurant cette période, bébé découvre le monde à travers ses sens. Sa vision s'affine progressivement, passant de formes floues à la reconnaissance des visages familiers.\n\n### 3-6 mois : L'interaction sociale\nVotre bébé commence à sourire intentionnellement et à gazouiller. C'est le début de la communication !\n\n### 6-12 mois : La mobilité\nCette période est marquée par l'acquisition de la motricité. Bébé apprend à se retourner, s'asseoir, ramper puis marcher.\n\n## Conseils pour accompagner le développement\n\n- **Stimulation douce** : Proposez des activités adaptées à son âge\n- **Patience** : Chaque enfant évolue à son rythme\n- **Observation** : Soyez attentif aux signaux de votre bébé\n- **Encouragement** : Célébrez chaque petite victoire\n\nLe développement de votre bébé est unique. Faites confiance à votre instinct parental et n'hésitez pas à consulter votre pédiatre pour toute question.",
    excerpt: "Découvrez les étapes essentielles du développement de bébé durant sa première année, de l'éveil sensoriel aux premiers pas.",
    image: "https://images.pexels.com/photos/1257110/pexels-photo-1257110.jpeg?auto=compress&cs=tinysrgb&w=800",
    status: "published",
    author_id: 1
  },
  {
    title: "Être maman : trouver l'équilibre entre maternité et bien-être personnel",
    content: "## Introduction\n\nDevenir maman est l'une des expériences les plus transformatrices de la vie d'une femme. Entre les joies immenses et les défis quotidiens, il est essentiel de trouver un équilibre qui préserve votre bien-être.\n\n## Prendre soin de soi\n\n### L'importance de l'auto-compassion\nÊtre maman ne signifie pas être parfaite. Accordez-vous le droit à l'erreur et célébrez vos réussites, même les plus petites.\n\n### Moments de pause\nMême 10 minutes par jour peuvent faire la différence. Que ce soit pour boire un thé en silence, lire quelques pages ou simplement respirer, ces moments vous appartiennent.\n\n### Maintenir ses passions\nVos centres d'intérêt d'avant la maternité font partie de votre identité. Trouvez des moyens créatifs de les intégrer dans votre nouvelle vie.\n\n## Construire son réseau de soutien\n\n- **Famille et amis** : N'hésitez pas à demander de l'aide\n- **Autres mamans** : Partagez vos expériences et conseils\n- **Professionnels** : Consultez sans hésiter en cas de besoin\n- **Communautés en ligne** : Trouvez du soutien et des ressources\n\n## Gérer la culpabilité maternelle\n\nLa culpabilité fait souvent partie du parcours maternel. Rappelez-vous qu'une maman épanouie contribue au bonheur de toute la famille.\n\nVotre parcours de maman est unique. Écoutez-vous, respectez vos besoins et souvenez-vous que vous faites de votre mieux.",
    excerpt: "Conseils pratiques pour concilier maternité et épanouissement personnel, en prenant soin de soi tout en étant une maman aimante.",
    image: "https://images.pexels.com/photos/1648377/pexels-photo-1648377.jpeg?auto=compress&cs=tinysrgb&w=800",
    status: "published",
    author_id: 1
  }
];

/**
 * Fonction simple pour créer un article
 */
const createSingleArticle = async (articleData: any) => {
  try {
    console.log(`📝 Création de l'article: "${articleData.title}"`);
    const response = await createArticle(articleData);
    console.log(`✅ Succès:`, response.data);
    return { success: true, data: response.data };
  } catch (error: any) {
    console.error(`❌ Erreur:`, error.response?.data || error.message);
    return { success: false, error: error.response?.data || error.message };
  }
};

/**
 * Créer les articles avec catégorie Bébé (ID: 1)
 */
const createWithBabyCategory = async () => {
  console.log('🍼 Création d\'articles avec catégorie "Bébé" (ID: 1)');
  
  const articleWithCategory = {
    ...testArticles[0],
    category_id: 1
  };
  
  return await createSingleArticle(articleWithCategory);
};

/**
 * Créer les articles avec catégorie Maman (ID: 2)
 */
const createWithMomCategory = async () => {
  console.log('👩 Création d\'articles avec catégorie "Maman" (ID: 2)');
  
  const articleWithCategory = {
    ...testArticles[1],
    category_id: 2
  };
  
  return await createSingleArticle(articleWithCategory);
};

/**
 * Créer tous les articles avec leurs catégories respectives
 */
const createAllArticles = async () => {
  console.log('🚀 Création de tous les articles avec catégories...');
  
  const results = [];
  
  // Article 1 : Bébé
  const result1 = await createWithBabyCategory();
  results.push(result1);
  
  // Pause
  console.log('⏳ Pause de 2 secondes...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Article 2 : Maman
  const result2 = await createWithMomCategory();
  results.push(result2);
  
  // Résumé
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  
  console.log('\n📊 Résumé:');
  console.log(`✅ Succès: ${successful}/2`);
  console.log(`❌ Échecs: ${failed}/2`);
  
  return results;
};

/**
 * Test simple de création d'un seul article
 */
const testSingleCreation = async () => {
  console.log('🧪 Test de création d\'un seul article...');
  
  const testArticle = {
    title: "Test Article",
    content: "Contenu de test simple",
    excerpt: "Extrait de test",
    image: "https://images.pexels.com/photos/1257110/pexels-photo-1257110.jpeg",
    status: "published",
    author_id: 1,
    category_id: 1
  };
  
  return await createSingleArticle(testArticle);
};

// Exposition globale
declare global {
  interface Window {
    simpleArticles: {
      createAll: typeof createAllArticles;
      createBaby: typeof createWithBabyCategory;
      createMom: typeof createWithMomCategory;
      testSingle: typeof testSingleCreation;
    };
  }
}

window.simpleArticles = {
  createAll: createAllArticles,
  createBaby: createWithBabyCategory,
  createMom: createWithMomCategory,
  testSingle: testSingleCreation
};

console.log('🔧 Script simple de création d\'articles chargé');
console.log('📋 Commandes disponibles:');
console.log('  window.simpleArticles.testSingle() - Test simple');
console.log('  window.simpleArticles.createBaby() - Article Bébé');
console.log('  window.simpleArticles.createMom() - Article Maman');
console.log('  window.simpleArticles.createAll() - Tous les articles');

/**
 * Fonction de test pour vérifier le bon fonctionnement de toFormData
 * avec des données d'article complexes
 */

import toFormData from './toFormData';

export const testArticleFormData = () => {
    console.log('🧪 Test de la fonction toFormData avec des données d\'article...');
    
    // Données de test similaires à celles envoyées par ArticleForm
    const testData = {
        title: "Test Article",
        author: "Test Author", 
        category_id: 1,
        sections: [
            {
                id: 1,
                title: "Section 1",
                order: 1,
                subsections: [
                    {
                        id: 1,
                        title: "Subsection 1",
                        order: 1,
                        paragraphs: [
                            {
                                id: 1,
                                content: "Contenu du paragraphe"
                            }
                        ]
                    }
                ]
            }
        ],
        images: [] // Pas de fichiers pour ce test
    };

    try {
        const formData = toFormData(testData);
        
        console.log('✅ FormData créé avec succès');
        console.log('📋 Contenu du FormData:');
        
        // Afficher le contenu du FormData
        for (let [key, value] of formData.entries()) {
            console.log(`  ${key}:`, value);
        }
        
        return true;
    } catch (error) {
        console.error('❌ Erreur lors de la création du FormData:', error);
        return false;
    }
};

// Fonction pour tester avec des fichiers simulés
export const testArticleFormDataWithFiles = () => {
    console.log('🧪 Test de la fonction toFormData avec des fichiers...');
    
    // Créer un fichier simulé pour le test
    const mockFile = new File(['test content'], 'test.jpg', { type: 'image/jpeg' });
    
    const testData = {
        title: "Test Article avec images",
        author: "Test Author",
        category_id: 1,
        sections: [],
        images: [mockFile]
    };

    try {
        const formData = toFormData(testData);
        
        console.log('✅ FormData avec fichiers créé avec succès');
        console.log('📋 Contenu du FormData:');
        
        for (let [key, value] of formData.entries()) {
            if (value instanceof File) {
                console.log(`  ${key}: File(${value.name}, ${value.size} bytes, ${value.type})`);
            } else {
                console.log(`  ${key}:`, value);
            }
        }
        
        return true;
    } catch (error) {
        console.error('❌ Erreur lors de la création du FormData avec fichiers:', error);
        return false;
    }
};

// Exposer les fonctions de test globalement pour les tests dans la console
if (typeof window !== 'undefined') {
    (window as any).testFormData = {
        testArticleFormData,
        testArticleFormDataWithFiles
    };
}

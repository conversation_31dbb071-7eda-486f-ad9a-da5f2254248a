/**
 * Test avec de vraies images (blobs d'images valides)
 */

import { createProduct, createArticle } from '../api/actions';

// Créer un blob d'image 1x1 pixel PNG valide
const createValidImageBlob = (name: string): File => {
    // PNG 1x1 pixel transparent en base64
    const pngData = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==';
    
    // Convertir base64 en bytes
    const byteCharacters = atob(pngData);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    
    // Créer un blob PNG valide
    const blob = new Blob([byteArray], { type: 'image/png' });
    
    // Convertir en File
    return new File([blob], name, { type: 'image/png' });
};

export const testProductWithRealImages = async () => {
    console.log('🧪 Test PRODUIT avec vraies images...');
    
    const realImage1 = createValidImageBlob('real-product1.png');
    const realImage2 = createValidImageBlob('real-product2.png');
    
    console.log('📋 Images créées:', {
        image1: { name: realImage1.name, size: realImage1.size, type: realImage1.type },
        image2: { name: realImage2.name, size: realImage2.size, type: realImage2.type }
    });

    const productPayload = {
        title: "Test Product Real Images",
        description: "Description test",
        price: 5000,
        inStock: 10,
        category_id: 1,
        images: [realImage1, realImage2]
    };

    try {
        const response = await createProduct(productPayload);
        console.log('✅ PRODUIT avec vraies images créé:', response.data);
        return { success: true, data: response.data };
    } catch (error: any) {
        console.error('❌ PRODUIT avec vraies images échoué:', error);
        if (error.response) {
            console.error('📋 Détails:', {
                status: error.response.status,
                data: error.response.data
            });
        }
        return { success: false, error: error.response?.data };
    }
};

export const testArticleWithRealImages = async () => {
    console.log('🧪 Test ARTICLE avec vraies images...');
    
    const realImage1 = createValidImageBlob('real-article1.png');
    const realImage2 = createValidImageBlob('real-article2.png');

    const articlePayload = {
        title: "Test Article Real Images",
        author: "Test Author",
        category_id: 1,
        sections: [],
        images: [realImage1, realImage2]
    };

    try {
        const response = await createArticle(articlePayload);
        console.log('✅ ARTICLE avec vraies images créé:', response.data);
        return { success: true, data: response.data };
    } catch (error: any) {
        console.error('❌ ARTICLE avec vraies images échoué:', error);
        if (error.response) {
            console.error('📋 Détails:', {
                status: error.response.status,
                data: error.response.data
            });
        }
        return { success: false, error: error.response?.data };
    }
};

export const testBothWithRealImages = async () => {
    console.log('🔍 TEST AVEC VRAIES IMAGES - Products vs Articles...');
    
    console.log('\n=== PRODUIT AVEC VRAIES IMAGES ===');
    const productResult = await testProductWithRealImages();
    
    console.log('\n=== ARTICLE AVEC VRAIES IMAGES ===');
    const articleResult = await testArticleWithRealImages();
    
    console.log('\n=== RÉSULTATS AVEC VRAIES IMAGES ===');
    console.log(`Produit avec vraies images: ${productResult.success ? '✅ SUCCÈS' : '❌ ÉCHEC'}`);
    console.log(`Article avec vraies images: ${articleResult.success ? '✅ SUCCÈS' : '❌ ÉCHEC'}`);
    
    if (productResult.success && articleResult.success) {
        console.log('🎉 LES DEUX FONCTIONNENT AVEC VRAIES IMAGES - Problème résolu !');
    } else if (productResult.success && !articleResult.success) {
        console.log('🚨 PRODUIT OK, ARTICLE KO - Problème spécifique backend Articles');
        console.log('Erreur article:', articleResult.error);
    } else if (!productResult.success && articleResult.success) {
        console.log('🤔 ARTICLE OK, PRODUIT KO - Inattendu');
    } else {
        console.log('❌ LES DEUX ÉCHOUENT même avec vraies images');
        console.log('Erreur produit:', productResult.error);
        console.log('Erreur article:', articleResult.error);
    }
    
    return { productResult, articleResult };
};

// Exposer globalement
if (typeof window !== 'undefined') {
    (window as any).testRealImages = {
        testProductWithRealImages,
        testArticleWithRealImages,
        testBothWithRealImages,
        createValidImageBlob
    };
    
    console.log('🔧 Tests vraies images disponibles:');
    console.log('  window.testRealImages.testProductWithRealImages()');
    console.log('  window.testRealImages.testArticleWithRealImages()');
    console.log('  window.testRealImages.testBothWithRealImages()');
}

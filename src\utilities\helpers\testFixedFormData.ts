/**
 * Test de la correction du FormData pour les images
 */

import toFormData from './toFormData';

export const testFixedImageFormData = () => {
    console.log('🧪 Test de la correction FormData pour images...');
    
    const mockImage1 = new File(['image content 1'], 'test1.jpg', { type: 'image/jpeg' });
    const mockImage2 = new File(['image content 2'], 'test2.png', { type: 'image/png' });

    const testData = {
        title: "Test Article",
        author: "Test Author",
        category_id: 1,
        sections: [
            {
                id: 1,
                title: "Section test",
                subsections: []
            }
        ],
        images: [mockImage1, mockImage2]
    };

    try {
        const formData = toFormData(testData);
        
        console.log('✅ FormData corrigé créé avec succès');
        console.log('📋 Nouveau contenu FormData:');
        
        for (let [key, value] of formData.entries()) {
            if (value instanceof File) {
                console.log(`  ${key}: File(${value.name}, ${value.size} bytes)`);
            } else {
                console.log(`  ${key}:`, value);
            }
        }
        
        // Vérifier spécifiquement le format des images
        const imageKeys = [];
        for (let [key, value] of formData.entries()) {
            if (value instanceof File) {
                imageKeys.push(key);
            }
        }
        
        console.log('🔍 Clés d\'images détectées:', imageKeys);
        
        if (imageKeys.includes('images[]')) {
            console.log('✅ Format images[] détecté - Compatible Laravel');
        } else {
            console.log('❌ Format images[] non détecté - Problème potentiel');
        }
        
        return formData;
    } catch (error) {
        console.error('❌ Erreur lors du test:', error);
        return null;
    }
};

// Exposer globalement
if (typeof window !== 'undefined') {
    (window as any).testFixedFormData = {
        testFixedImageFormData
    };
}

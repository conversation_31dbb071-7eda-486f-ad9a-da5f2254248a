/**
 * Test du format backend attendu pour les images
 */

export const testBackendImageFormat = () => {
    console.log('🧪 Test du format backend attendu pour les images...');
    
    const mockFile1 = new File(['image content 1'], 'backend1.jpg', { type: 'image/jpeg' });
    const mockFile2 = new File(['image content 2'], 'backend2.png', { type: 'image/png' });

    // Format attendu par le backend selon les spécifications
    const backendFormat = {
        title: "Test Backend Format",
        author: "Test Author",
        category_id: 1,
        sections: [
            {
                id: 1,
                title: "Section test",
                order: 1,
                subsections: [
                    {
                        id: 1,
                        title: "Subsection test",
                        order: 1,
                        paragraphs: [
                            {
                                id: 1,
                                content: "Contenu test"
                            }
                        ]
                    }
                ]
            }
        ],
        images: [
            {
                id: 1,
                url: mockFile1, // Blob/File dans url pour POST
                caption: "Image 1 caption",
                order: 1
            },
            {
                id: 2,
                url: mockFile2, // Blob/File dans url pour POST
                caption: "Image 2 caption", 
                order: 2
            }
        ]
    };

    console.log('📋 Format backend pour POST:', {
        title: backendFormat.title,
        author: backendFormat.author,
        category_id: backendFormat.category_id,
        sectionsCount: backendFormat.sections.length,
        imagesCount: backendFormat.images.length,
        imageStructure: backendFormat.images.map(img => ({
            id: img.id,
            urlType: typeof img.url,
            isFile: img.url instanceof File,
            caption: img.caption,
            order: img.order
        }))
    });

    return backendFormat;
};

export const testDirectBackendCall = async () => {
    console.log('🧪 Test appel direct avec format backend...');
    
    const mockFile = new File(['test content'], 'backend-test.jpg', { type: 'image/jpeg' });
    
    const payload = {
        title: "Test Direct Backend",
        author: "Test Author",
        category_id: 1,
        sections: [],
        images: [
            {
                id: 1,
                url: mockFile,
                caption: "Test image",
                order: 1
            }
        ]
    };

    try {
        // Appel direct à l'API avec le nouveau format
        const response = await fetch('http://localhost:5173/api/article/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            const errorData = await response.json();
            console.error('❌ Erreur backend format:', errorData);
            return null;
        }

        const result = await response.json();
        console.log('✅ Succès avec format backend:', result);
        return result;

    } catch (error) {
        console.error('❌ Erreur appel direct backend:', error);
        return null;
    }
};

export const compareFormats = () => {
    console.log('🔍 Comparaison des formats...');
    
    const mockFile = new File(['test'], 'compare.jpg', { type: 'image/jpeg' });
    
    console.log('\n📋 Format ANCIEN (File[]):');
    const oldFormat = [mockFile];
    console.log('  Type:', typeof oldFormat[0]);
    console.log('  Instance:', oldFormat[0].constructor.name);
    console.log('  Properties:', Object.getOwnPropertyNames(oldFormat[0]));
    
    console.log('\n📋 Format NOUVEAU (Backend attendu):');
    const newFormat = [
        {
            id: 1,
            url: mockFile,
            caption: "Test",
            order: 1
        }
    ];
    console.log('  Structure:', newFormat[0]);
    console.log('  url Type:', typeof newFormat[0].url);
    console.log('  url Instance:', newFormat[0].url.constructor.name);
};

// Exposer globalement
if (typeof window !== 'undefined') {
    (window as any).testBackendFormat = {
        testBackendImageFormat,
        testDirectBackendCall,
        compareFormats
    };
    
    console.log('🔧 Tests format backend disponibles:');
    console.log('  window.testBackendFormat.testBackendImageFormat()');
    console.log('  window.testBackendFormat.testDirectBackendCall()');
    console.log('  window.testBackendFormat.compareFormats()');
}

/**
 * Test du format EXACT spécifié par le développeur backend
 */

import { createArticle } from '../api/actions';

// Créer un blob d'image valide
const createImageBlob = (name: string): File => {
    const pngData = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==';
    const byteCharacters = atob(pngData);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: 'image/png' });
    return new File([blob], name, { type: 'image/png' });
};

export const testBackendExactFormat = async () => {
    console.log('🧪 Test FORMAT EXACT spécifié par le développeur backend...');
    
    const imageFile1 = createImageBlob('backend-exact1.png');
    const imageFile2 = createImageBlob('backend-exact2.png');

    // FORMAT EXACT selon spécification backend
    const payload = {
        title: "Test Backend Exact Format",
        author: "Test Author",
        category_id: 1,
        sections: [
            {
                id: 1,
                title: "Section test",
                order: 1,
                subsections: []
            }
        ],
        images: [
            {
                id: 1,
                url: imageFile1,  // File qui sera converti en Blob
                caption: "Les étapes du développement de bébé",
                order: 1
            },
            {
                id: 2,
                url: imageFile2,  // File qui sera converti en Blob
                caption: "Image de test 2",
                order: 2
            }
        ]
    };

    console.log('📤 Payload format backend exact:', {
        title: payload.title,
        author: payload.author,
        category_id: payload.category_id,
        sectionsCount: payload.sections.length,
        imagesCount: payload.images.length,
        imageStructure: payload.images.map(img => ({
            id: img.id,
            urlType: typeof img.url,
            isFile: img.url instanceof File,
            caption: img.caption,
            order: img.order
        }))
    });

    try {
        const response = await createArticle(payload);
        console.log('✅ SUCCÈS avec format backend exact:', response.data);
        return { success: true, data: response.data };
    } catch (error: any) {
        console.error('❌ ÉCHEC avec format backend exact:', error);
        if (error.response) {
            console.error('📋 Détails erreur:', {
                status: error.response.status,
                data: error.response.data
            });
        }
        return { success: false, error: error.response?.data };
    }
};

export const testMinimalBackendFormat = async () => {
    console.log('🧪 Test format backend MINIMAL...');
    
    const imageFile = createImageBlob('minimal-backend.png');

    const payload = {
        title: "Test Minimal Backend",
        author: "Test Author",
        category_id: 1,
        sections: [],  // Vide
        images: [
            {
                id: 1,
                url: imageFile,
                caption: "Test minimal",
                order: 1
            }
        ]
    };

    try {
        const response = await createArticle(payload);
        console.log('✅ SUCCÈS format minimal backend:', response.data);
        return { success: true, data: response.data };
    } catch (error: any) {
        console.error('❌ ÉCHEC format minimal backend:', error);
        if (error.response) {
            console.error('📋 Détails:', error.response.data);
        }
        return { success: false, error: error.response?.data };
    }
};

export const testBackendFormatComplete = async () => {
    console.log('🔍 TEST COMPLET FORMAT BACKEND...');
    
    console.log('\n=== TEST FORMAT EXACT ===');
    const exactResult = await testBackendExactFormat();
    
    console.log('\n=== TEST FORMAT MINIMAL ===');
    const minimalResult = await testMinimalBackendFormat();
    
    console.log('\n=== RÉSULTATS FORMAT BACKEND ===');
    console.log(`Format exact: ${exactResult.success ? '✅ SUCCÈS' : '❌ ÉCHEC'}`);
    console.log(`Format minimal: ${minimalResult.success ? '✅ SUCCÈS' : '❌ ÉCHEC'}`);
    
    if (exactResult.success && minimalResult.success) {
        console.log('🎉 FORMAT BACKEND FONCTIONNE - Problème résolu !');
    } else {
        console.log('❌ Format backend ne fonctionne pas encore');
        if (!exactResult.success) {
            console.log('Erreur format exact:', exactResult.error);
        }
        if (!minimalResult.success) {
            console.log('Erreur format minimal:', minimalResult.error);
        }
    }
    
    return { exactResult, minimalResult };
};

// Exposer globalement
if (typeof window !== 'undefined') {
    (window as any).testBackendExact = {
        testBackendExactFormat,
        testMinimalBackendFormat,
        testBackendFormatComplete
    };
    
    console.log('🔧 Tests format backend exact disponibles:');
    console.log('  window.testBackendExact.testBackendExactFormat()');
    console.log('  window.testBackendExact.testMinimalBackendFormat()');
    console.log('  window.testBackendExact.testBackendFormatComplete()');
}

/**
 * Test du NOUVEAU format backend directement
 */

import { createArticle } from '../api/actions';

export const testNewFormatDirectly = async () => {
    console.log('🧪 Test NOUVEAU format backend directement...');
    
    const mockImage1 = new File(['image content 1'], 'new1.jpg', { type: 'image/jpeg' });
    const mockImage2 = new File(['image content 2'], 'new2.png', { type: 'image/png' });

    // NOUVEAU FORMAT comme dans AddArticle.tsx
    const transformedImages = [mockImage1, mockImage2].map((file, index) => ({
        id: Date.now() + index,
        url: file, // File dans url
        caption: `Caption ${index + 1}`,
        order: index + 1
    }));

    const payload = {
        title: "Test Nouveau Format Direct",
        author: "Test Author",
        category_id: 1,
        sections: [
            {
                id: Date.now(),
                title: "Section test nouveau",
                order: 1,
                subsections: []
            }
        ],
        images: transformedImages // Format backend attendu
    };

    console.log('📤 Payload nouveau format:', {
        title: payload.title,
        author: payload.author,
        category_id: payload.category_id,
        sectionsCount: payload.sections.length,
        imagesCount: payload.images.length,
        imageFormat: payload.images.map(img => ({
            id: img.id,
            urlType: typeof img.url,
            isFile: img.url instanceof File,
            caption: img.caption,
            order: img.order
        }))
    });

    try {
        const response = await createArticle(payload);
        console.log('✅ Succès avec nouveau format:', response.data);
        return response.data;
    } catch (error: any) {
        console.error('❌ Erreur nouveau format:', error);
        if (error.response) {
            console.error('📋 Détails erreur:', {
                status: error.response.status,
                data: error.response.data
            });
        }
        return null;
    }
};

export const testMinimalNewFormat = async () => {
    console.log('🧪 Test format minimal nouveau...');
    
    const mockImage = new File(['minimal'], 'minimal.jpg', { type: 'image/jpeg' });

    const payload = {
        title: "Test Minimal Nouveau",
        author: "Test Author",
        category_id: 1,
        sections: [], // Vide
        images: [
            {
                id: 1,
                url: mockImage,
                caption: "Test minimal",
                order: 1
            }
        ]
    };

    console.log('📤 Payload minimal:', payload);

    try {
        const response = await createArticle(payload);
        console.log('✅ Succès minimal:', response.data);
        return response.data;
    } catch (error: any) {
        console.error('❌ Erreur minimal:', error);
        if (error.response) {
            console.error('📋 Détails:', error.response.data);
        }
        return null;
    }
};

export const compareOldVsNew = async () => {
    console.log('🔍 Comparaison ancien vs nouveau format...');
    
    const mockImage = new File(['compare'], 'compare.jpg', { type: 'image/jpeg' });

    // Format ANCIEN
    console.log('\n📋 Test format ANCIEN:');
    const oldPayload = {
        title: "Test Ancien Format",
        author: "Test Author",
        category_id: 1,
        sections: [],
        images: [mockImage] // File[] directement
    };

    try {
        await createArticle(oldPayload);
        console.log('✅ Ancien format fonctionne');
    } catch (error: any) {
        console.error('❌ Ancien format échoue:', error.response?.data?.message);
    }

    // Format NOUVEAU
    console.log('\n📋 Test format NOUVEAU:');
    const newPayload = {
        title: "Test Nouveau Format",
        author: "Test Author",
        category_id: 1,
        sections: [],
        images: [
            {
                id: 1,
                url: mockImage,
                caption: "Test nouveau",
                order: 1
            }
        ]
    };

    try {
        await createArticle(newPayload);
        console.log('✅ Nouveau format fonctionne');
    } catch (error: any) {
        console.error('❌ Nouveau format échoue:', error.response?.data?.message);
    }
};

// Exposer globalement
if (typeof window !== 'undefined') {
    (window as any).testNewFormat = {
        testNewFormatDirectly,
        testMinimalNewFormat,
        compareOldVsNew
    };
    
    console.log('🔧 Tests nouveau format disponibles:');
    console.log('  window.testNewFormat.testNewFormatDirectly()');
    console.log('  window.testNewFormat.testMinimalNewFormat()');
    console.log('  window.testNewFormat.compareOldVsNew()');
}

/**
 * Test de la conversion File vers Blob
 */

import { createArticle } from '../api/actions';

export const testBlobConversion = async () => {
    console.log('🧪 Test conversion File vers Blob...');
    
    const mockFile = new File(['test blob content'], 'blob-test.jpg', { type: 'image/jpeg' });
    
    // Test de conversion manuelle
    console.log('📋 File original:', {
        name: mockFile.name,
        size: mockFile.size,
        type: mockFile.type,
        constructor: mockFile.constructor.name
    });
    
    // Conversion en Blob
    const blob = new Blob([await mockFile.arrayBuffer()], { type: mockFile.type });
    
    console.log('📋 Blob converti:', {
        size: blob.size,
        type: blob.type,
        constructor: blob.constructor.name
    });
    
    return { file: mockFile, blob };
};

export const testArticleWithBlobs = async () => {
    console.log('🧪 Test article avec conversion Blob...');
    
    const mockFile = new File(['blob content'], 'article-blob.jpg', { type: 'image/jpeg' });
    
    const payload = {
        title: "Test Article Blob",
        author: "Test Author",
        category_id: 1,
        sections: [],
        images: [
            {
                id: 1,
                url: mockFile,
                caption: "Test blob image",
                order: 1
            }
        ]
    };

    console.log('📤 Envoi avec nouveau format (conversion Blob automatique)...');

    try {
        const response = await createArticle(payload);
        console.log('✅ Succès avec conversion Blob:', response.data);
        return response.data;
    } catch (error: any) {
        console.error('❌ Erreur avec conversion Blob:', error);
        if (error.response) {
            console.error('📋 Détails erreur Blob:', {
                status: error.response.status,
                data: error.response.data
            });
        }
        return null;
    }
};

export const testMinimalBlob = async () => {
    console.log('🧪 Test minimal avec Blob...');
    
    const mockFile = new File(['minimal blob'], 'minimal-blob.jpg', { type: 'image/jpeg' });
    
    const payload = {
        title: "Test Minimal Blob",
        author: "Test Author",
        category_id: 1,
        sections: [],
        images: [
            {
                id: 1,
                url: mockFile,
                caption: "",
                order: 1
            }
        ]
    };

    try {
        const response = await createArticle(payload);
        console.log('✅ Succès minimal Blob:', response.data);
        return response.data;
    } catch (error: any) {
        console.error('❌ Erreur minimal Blob:', error);
        if (error.response) {
            console.error('📋 Détails minimal:', error.response.data);
        }
        return null;
    }
};

// Exposer globalement
if (typeof window !== 'undefined') {
    (window as any).testBlob = {
        testBlobConversion,
        testArticleWithBlobs,
        testMinimalBlob
    };
    
    console.log('🔧 Tests Blob disponibles:');
    console.log('  window.testBlob.testBlobConversion()');
    console.log('  window.testBlob.testArticleWithBlobs()');
    console.log('  window.testBlob.testMinimalBlob()');
}

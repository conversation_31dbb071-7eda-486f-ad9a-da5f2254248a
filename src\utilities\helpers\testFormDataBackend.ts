/**
 * Test FormData avec structure backend
 */

import toFormData from './toFormData';
import { createArticle } from '../api/actions';

// Créer un blob d'image valide
const createImageBlob = (name: string): File => {
    const pngData = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==';
    const byteCharacters = atob(pngData);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: 'image/png' });
    return new File([blob], name, { type: 'image/png' });
};

export const testFormDataBackendStructure = () => {
    console.log('🧪 Test FormData avec structure backend...');
    
    const imageFile1 = createImageBlob('formdata1.png');
    const imageFile2 = createImageBlob('formdata2.png');

    const testData = {
        title: "Test FormData Backend",
        author: "Test Author",
        category_id: 1,
        sections: [
            {
                id: 1,
                title: "Section test",
                order: 1,
                subsections: []
            }
        ],
        images: [
            {
                id: 1,
                url: imageFile1,
                caption: "Image 1 caption",
                order: 1
            },
            {
                id: 2,
                url: imageFile2,
                caption: "Image 2 caption",
                order: 2
            }
        ]
    };

    try {
        const formData = toFormData(testData);
        
        console.log('✅ FormData avec structure backend créé');
        console.log('📋 Contenu FormData:');
        
        for (let [key, value] of formData.entries()) {
            if (value instanceof File) {
                console.log(`  ${key}: File(${value.name}, ${value.size} bytes)`);
            } else {
                console.log(`  ${key}: ${value}`);
            }
        }
        
        // Vérifier la structure backend
        const hasImageStructure = Array.from(formData.keys()).some(key => 
            key.includes('images[0][id]') || 
            key.includes('images[0][url]') || 
            key.includes('images[0][caption]') || 
            key.includes('images[0][order]')
        );
        
        console.log('\n🔍 Vérification structure backend:');
        console.log(`  Structure images backend présente: ${hasImageStructure ? '✅' : '❌'}`);
        
        return formData;
    } catch (error) {
        console.error('❌ Erreur FormData backend:', error);
        return null;
    }
};

export const testArticleFormDataBackend = async () => {
    console.log('🧪 Test article avec FormData structure backend...');
    
    const imageFile = createImageBlob('article-formdata.png');

    const payload = {
        title: "Test Article FormData Backend",
        author: "Test Author",
        category_id: 1,
        sections: [],
        images: [
            {
                id: 1,
                url: imageFile,
                caption: "Test FormData backend",
                order: 1
            }
        ]
    };

    try {
        const response = await createArticle(payload);
        console.log('✅ SUCCÈS avec FormData structure backend:', response.data);
        return { success: true, data: response.data };
    } catch (error: any) {
        console.error('❌ ÉCHEC avec FormData structure backend:', error);
        if (error.response) {
            console.error('📋 Détails erreur:', {
                status: error.response.status,
                data: error.response.data
            });
        }
        return { success: false, error: error.response?.data };
    }
};

// Exposer globalement
if (typeof window !== 'undefined') {
    (window as any).testFormDataBackend = {
        testFormDataBackendStructure,
        testArticleFormDataBackend
    };
    
    console.log('🔧 Tests FormData backend disponibles:');
    console.log('  window.testFormDataBackend.testFormDataBackendStructure()');
    console.log('  window.testFormDataBackend.testArticleFormDataBackend()');
}

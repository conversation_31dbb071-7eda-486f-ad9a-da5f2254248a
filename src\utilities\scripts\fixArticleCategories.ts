/**
 * Script pour récupérer les catégories et créer des articles avec category_id valide
 */

import { getAllCategories } from '../api/admin/actions';
import { createArticle } from '../api/actions';

// Articles simplifiés avec structure API attendue
const simpleArticles = [
  {
    title: "Le développement de bébé : les étapes clés de la première année",
    content: `## Introduction

La première année de vie de votre bébé est marquée par des transformations extraordinaires. Chaque mois apporte son lot de nouvelles découvertes et d'acquisitions qui témoignent du développement rapide de votre petit trésor.

## Les premiers mois : l'éveil sensoriel

### 0-3 mois : Les fondations
Durant cette période, bébé découvre le monde à travers ses sens. Sa vision s'affine progressivement, passant de formes floues à la reconnaissance des visages familiers. L'ouïe se développe et il commence à réagir aux voix qu'il connaît.

### 3-6 mois : L'interaction sociale
Votre bébé commence à sourire intentionnellement et à gazouiller. C'est le début de la communication ! Il découvre ses mains et commence à les porter à sa bouche, développant ainsi sa coordination.

### 6-12 mois : La mobilité
Cette période est marquée par l'acquisition de la motricité. Bébé apprend à se retourner, s'asseoir, ramper puis marcher. Chaque étape est une victoire !

## Conseils pour accompagner le développement

- **Stimulation douce** : Proposez des activités adaptées à son âge
- **Patience** : Chaque enfant évolue à son rythme
- **Observation** : Soyez attentif aux signaux de votre bébé
- **Encouragement** : Célébrez chaque petite victoire

Le développement de votre bébé est unique. Faites confiance à votre instinct parental et n'hésitez pas à consulter votre pédiatre pour toute question.`,
    excerpt: "Découvrez les étapes essentielles du développement de bébé durant sa première année, de l'éveil sensoriel aux premiers pas.",
    image: "https://images.pexels.com/photos/1257110/pexels-photo-1257110.jpeg?auto=compress&cs=tinysrgb&w=800",
    status: "published",
    author_id: 1
  },
  {
    title: "Être maman : trouver l'équilibre entre maternité et bien-être personnel",
    content: `## Introduction

Devenir maman est l'une des expériences les plus transformatrices de la vie d'une femme. Entre les joies immenses et les défis quotidiens, il est essentiel de trouver un équilibre qui préserve votre bien-être.

## Prendre soin de soi

### L'importance de l'auto-compassion
Être maman ne signifie pas être parfaite. Accordez-vous le droit à l'erreur et célébrez vos réussites, même les plus petites. Votre bien-être mental est crucial pour toute la famille.

### Moments de pause
Même 10 minutes par jour peuvent faire la différence. Que ce soit pour boire un thé en silence, lire quelques pages ou simplement respirer, ces moments vous appartiennent.

### Maintenir ses passions
Vos centres d'intérêt d'avant la maternité font partie de votre identité. Trouvez des moyens créatifs de les intégrer dans votre nouvelle vie, même de façon adaptée.

## Construire son réseau de soutien

- **Famille et amis** : N'hésitez pas à demander de l'aide
- **Autres mamans** : Partagez vos expériences et conseils
- **Professionnels** : Consultez sans hésiter en cas de besoin
- **Communautés en ligne** : Trouvez du soutien et des ressources

## Gérer la culpabilité maternelle

La culpabilité fait souvent partie du parcours maternel. Rappelez-vous qu'une maman épanouie contribue au bonheur de toute la famille. Prendre soin de vous n'est pas égoïste, c'est nécessaire.

Votre parcours de maman est unique. Écoutez-vous, respectez vos besoins et souvenez-vous que vous faites de votre mieux.`,
    excerpt: "Conseils pratiques pour concilier maternité et épanouissement personnel, en prenant soi tout en étant une maman aimante.",
    image: "https://images.pexels.com/photos/1648377/pexels-photo-1648377.jpeg?auto=compress&cs=tinysrgb&w=800",
    status: "published",
    author_id: 1
  }
];

/**
 * Récupère les catégories disponibles
 */
export const fetchAvailableCategories = async () => {
  console.log('🔍 Récupération des catégories disponibles...');
  
  try {
    const response = await getAllCategories();
    const categories = response.data;
    
    console.log('✅ Catégories récupérées:', categories);
    console.log(`📊 Nombre de catégories: ${categories?.length || 0}`);
    
    if (categories && categories.length > 0) {
      console.log('\n📋 Liste des catégories:');
      categories.forEach((cat: any, index: number) => {
        console.log(`  ${index + 1}. ID: ${cat.id} - Nom: "${cat.name}"`);
      });
    }
    
    return categories;
  } catch (error: any) {
    console.error('❌ Erreur lors de la récupération des catégories:', error);
    console.error('📊 Détails:', error.response?.data);
    return null;
  }
};

/**
 * Crée des articles avec category_id valide
 */
export const createArticlesWithCategories = async () => {
  console.log('🚀 Début de la création d\'articles avec catégories...');
  
  // 1. Récupérer les catégories
  const categories = await fetchAvailableCategories();
  
  if (!categories || categories.length === 0) {
    console.log('⚠️ Aucune catégorie disponible. Création d\'articles sans category_id...');
    return await createArticlesWithoutCategory();
  }
  
  // 2. Utiliser la première catégorie disponible
  const defaultCategory = categories[0];
  console.log(`✅ Utilisation de la catégorie: "${defaultCategory.name}" (ID: ${defaultCategory.id})`);
  
  // 3. Créer les articles avec category_id
  const results = [];
  
  for (let i = 0; i < simpleArticles.length; i++) {
    const article = {
      ...simpleArticles[i],
      category_id: defaultCategory.id
    };
    
    console.log(`\n📝 Création de l'article ${i + 1}/${simpleArticles.length}: "${article.title}"`);
    
    try {
      const response = await createArticle(article);
      console.log(`✅ Article créé avec succès`);
      results.push({ success: true, title: article.title });
    } catch (error: any) {
      console.error(`❌ Erreur lors de la création:`, error.response?.data);
      results.push({ success: false, title: article.title, error: error.response?.data });
    }
    
    // Pause entre les requêtes
    if (i < simpleArticles.length - 1) {
      console.log('⏳ Pause de 2 secondes...');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  // Résumé
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  
  console.log('\n📊 Résumé final:');
  console.log(`✅ Succès: ${successful}/${simpleArticles.length}`);
  console.log(`❌ Échecs: ${failed}/${simpleArticles.length}`);
  
  return results;
};

/**
 * Crée des articles sans category_id (fallback)
 */
const createArticlesWithoutCategory = async () => {
  console.log('📝 Création d\'articles sans category_id...');
  
  const results = [];
  
  for (let i = 0; i < simpleArticles.length; i++) {
    const article = simpleArticles[i];
    
    console.log(`\n📝 Création de l'article ${i + 1}/${simpleArticles.length}: "${article.title}"`);
    
    try {
      const response = await createArticle(article);
      console.log(`✅ Article créé avec succès`);
      results.push({ success: true, title: article.title });
    } catch (error: any) {
      console.error(`❌ Erreur lors de la création:`, error.response?.data);
      results.push({ success: false, title: article.title, error: error.response?.data });
    }
    
    if (i < simpleArticles.length - 1) {
      console.log('⏳ Pause de 2 secondes...');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  return results;
};

// Exposition globale pour utilisation dans la console
declare global {
  interface Window {
    articleCategoryFix: {
      fetchCategories: typeof fetchAvailableCategories;
      createWithCategories: typeof createArticlesWithCategories;
    };
  }
}

window.articleCategoryFix = {
  fetchCategories: fetchAvailableCategories,
  createWithCategories: createArticlesWithCategories
};

console.log('🔧 Script de correction des catégories d\'articles chargé');
console.log('📋 Commandes disponibles:');
console.log('  window.articleCategoryFix.fetchCategories() - Voir les catégories');
console.log('  window.articleCategoryFix.createWithCategories() - Créer articles avec catégories');
